import pickle
import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans
from multiprocessing import Pool, cpu_count

trans = Trans()
geo = pickle.load(open('data/geo_info.pkl', 'rb'))

# 预处理子区域数据，避免重复构建
def _preprocess_subregions(geo_info):
    """预处理子区域数据，将坐标转换为Shapely多边形对象"""
    from shapely.geometry import Polygon

    subregions_polygons = {}
    for k in range(1, 5):
        subregion_key = f'subregion_{k}'
        if subregion_key in geo_info:
            try:
                subregions_polygons[subregion_key] = Polygon(geo_info[subregion_key])
            except Exception:
                # 如果创建多边形失败，跳过这个子区域
                continue
    return subregions_polygons

# 全局预处理子区域数据
SUBREGIONS_POLYGONS = _preprocess_subregions(geo)


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def _get_ship_subregion(position, subregions_polygons):
    """判断船舶位置属于哪个子区域"""
    from shapely.geometry import Point

    lon, lat = position
    point = Point(lon, lat)

    for subregion_name, subregion_polygon in subregions_polygons.items():
        if point.within(subregion_polygon):
            return subregion_name

    return None


def _process_time_group(args):
    """处理单个时间组的冲突计算"""
    postime, ship_data = args
    conflicts = []
    n_ships = len(ship_data)

    # 计算所有船舶对的冲突
    for i in range(n_ships):
        for j in range(i + 1, n_ships):
            ship1, ship2 = ship_data[i], ship_data[j]

            conflict = overlap_ratios_shapely(
                ship1[1], ship1[2], ship1[3], ship1[4], ship1[5],  # X,Y,A,B,Cog
                ship2[1], ship2[2], ship2[3], ship2[4], ship2[5]
            )

            # 如果两船都是Type=0（非穿越船），需要检查子航道
            if ship1[7] == 0 and ship2[7] == 0:  # 假设Type在索引7
                if SUBREGIONS_POLYGONS:
                    # 判断两船的子区域
                    ship1_subregion = _get_ship_subregion((ship1[1], ship1[2]), SUBREGIONS_POLYGONS)
                    ship2_subregion = _get_ship_subregion((ship2[1], ship2[2]), SUBREGIONS_POLYGONS)

                    # 如果两船都属于某个子区域但不是同一个，冲突设为0
                    if (ship1_subregion is not None and ship2_subregion is not None
                            and ship1_subregion != ship2_subregion):
                        conflict = 0

            conflicts.append({
                'PosTime': postime,
                'conflict': conflict,
                'id_pair': (int(ship1[6]), int(ship2[6])),  # ID
                'mmsi_pair': (int(ship1[0]), int(ship2[0]))  # MMSI
            })

    return conflicts


def conflict_calculation_part(df, segment_duration=300):  # 默认5分钟(300秒)
    # 获取时间范围
    min_time = int(df['PosTime'].min())
    max_time = int(df['PosTime'].max())

    # 随机选择起始时间，确保能容纳完整时间段
    if max_time - min_time < segment_duration:
        start_time = min_time
        end_time = max_time
    else:
        start_time = int(np.random.uniform(min_time, max_time - segment_duration))
        end_time = start_time + segment_duration

    print(f"随机选择时间段: {start_time} - {end_time} (持续{segment_duration}秒)")

    # 筛选时间段内的数据
    segment_df = df[(df['PosTime'] >= start_time) & (df['PosTime'] <= end_time)]

    # 按时间分组处理
    grouped = segment_df.groupby('PosTime')

    # 准备并行处理的数据
    time_groups = [(postime, dff[['MMSI', 'X', 'Y', 'A', 'B', 'Cog', 'ID', 'Type']].values)
                   for postime, dff in grouped]

    # 使用多进程并行处理
    print(f"使用 {cpu_count()} 个进程并行处理 {len(time_groups)} 个时间组")
    try:
        with Pool(processes=cpu_count()) as pool:
            results = list(tqdm(pool.imap(_process_time_group, time_groups, chunksize=1),
                               total=len(time_groups), desc="Processing time groups (parallel)"))
    except Exception as e:
        print(f"多进程处理出错，切换到单进程模式: {e}")
        results = []
        for time_group in tqdm(time_groups, desc="Processing time groups (single process)"):
            results.append(_process_time_group(time_group))

    # 合并结果
    all_conflicts = []
    for conflicts in results:
        all_conflicts.extend(conflicts)

    print(f"处理了 {len(grouped)} 个时间点，生成 {len(all_conflicts)} 条冲突记录")
    return pd.DataFrame(all_conflicts)


def conflict_calculation_all(df):
    # 按时间分组处理
    grouped = df.groupby('PosTime')

    # 准备并行处理的数据
    time_groups = [(postime, dff[['MMSI', 'X', 'Y', 'A', 'B', 'Cog', 'ID', 'Type']].values)
                   for postime, dff in grouped]

    # 使用多进程并行处理
    print(f"使用 {cpu_count()} 个进程并行处理 {len(time_groups)} 个时间组")
    try:
        with Pool(processes=cpu_count()) as pool:
            results = list(tqdm(pool.imap(_process_time_group, time_groups, chunksize=1),
                               total=len(time_groups), desc="Processing all time groups (parallel)"))
    except Exception as e:
        print(f"多进程处理出错，切换到单进程模式: {e}")
        results = []
        for time_group in tqdm(time_groups, desc="Processing all time groups (single process)"):
            results.append(_process_time_group(time_group))

    # 合并结果
    all_conflicts = []
    for conflicts in results:
        all_conflicts.extend(conflicts)

    print(f"处理了 {len(grouped)} 个时间点，生成 {len(all_conflicts)} 条冲突记录")
    return pd.DataFrame(all_conflicts)


def main():
    """主函数，用于Windows多进程兼容性"""
    trajectory_note_df = pd.read_parquet('data/2024_1_inter_note.parquet')

    # 筛选（xx小时）的数据
    min_time = trajectory_note_df['PosTime'].min() + 12 * 3600
    max_time_h = min_time + 24 * 3600  # 小时：3600秒
    trajectory_note_df = trajectory_note_df[trajectory_note_df['PosTime'] <= max_time_h]
    trajectory_note_df.reset_index(drop=True, inplace=True)

    print(f"筛选前48小时数据: {len(trajectory_note_df)} 条记录")

    conflict_calculation_all_df = conflict_calculation_all(trajectory_note_df)
    conflict_calculation_all_df.to_parquet('result/2024_1/conflicts_part.parquet')
    # conflicts_part_df = conflict_calculation_part(trajectory_note_df)
    # conflicts_part_df.head()


if __name__ == '__main__':
    main()
