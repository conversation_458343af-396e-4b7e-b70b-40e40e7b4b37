#%%
import pandas as pd
conflicts_part_df = pd.read_parquet('result/2024_1/conflicts_part.parquet')
trajectory_note_df = pd.read_parquet('data/2024_1_inter_note.parquet')
#%%
conflicts_part_df.head()
#%%
trajectory_note_df.head()
#%%
import networkx as nx
import numpy as np
from itertools import chain
from tqdm import tqdm
import pandas as pd

import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.pyplot import cm
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗

for postime in np.arange(1704092660, 1704093260+1):
    aggregated_conflicts = conflicts_part_df.loc[(conflicts_part_df['PosTime']==postime) & (conflicts_part_df['conflict']>0)]
    Gd = nx.Graph()
    Gd.add_nodes_from(list(np.unique(list(chain.from_iterable(aggregated_conflicts['id_pair'])))))

    # Extract first and second elements from tuples
    ship_pair1 = [pair[0] for pair in aggregated_conflicts['id_pair']]
    ship_pair2 = [pair[1] for pair in aggregated_conflicts['id_pair']]

    edges = [(int(a), int(b), {'weight': float(c)}) for a, b, c in zip(ship_pair1, ship_pair2, aggregated_conflicts['conflict'])]
    Gd.add_edges_from(edges)
    break
#%%
def vis(nx_graph):
    fig, ax = plt.subplots(figsize=(8, 5))
    seed = 4
    # 优化布局参数
    pos = nx.forceatlas2_layout(nx_graph, gravity=8, seed=seed)  # Scale the layout

    # Calculate edge weights and colors
    weights = [nx_graph[u][v]['weight'] for u, v in nx_graph.edges()]
    edge_colors = weights
    camp = cm.YlOrRd
    # Draw the graph
    # plt.subplot(121)
    nx.draw(nx_graph, pos,
            with_labels=True,
            node_color='lightblue',
            edge_color=edge_colors,
            edge_cmap=camp,
            width=[2 for w in weights],  # Scale edge widths
            node_size=200,  # Increase node size for better visibility
            font_size=8,
            font_weight='bold',
            font_family='Times New Roman')

    sm = plt.cm.ScalarMappable(cmap=camp,
                               norm=plt.Normalize(vmin=0, vmax=1))
    sm.set_array([])  # Necessary for the colorbar
    plt.colorbar(sm, ax=ax, shrink=1, aspect=25)  # aspect越大-越细

    # —— 在这里添加一个无填充的 Rectangle 作为边框 —— #
    rect = Rectangle((0, 0), 1, 1,
                     transform=ax.transAxes,  # 以 Axes 归一化坐标为单位
                     linewidth=1,
                     edgecolor='black',
                     facecolor='none',
                     zorder=10)
    ax.add_patch(rect)

    plt.tight_layout(pad=2)
    # plt.savefig(f'vis/network/{title}.svg', dpi=600)

    plt.show()
    plt.close()
vis(Gd)