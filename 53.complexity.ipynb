#%%
import pandas as pd
conflicts_part_df = pd.read_parquet('result/2024_1/conflicts_part.parquet')
trajectory_note_df = pd.read_parquet('data/2024_1_inter_note.parquet')
#%%
conflicts_part_df.head()
#%%
import networkx as nx
import numpy as np
from itertools import chain
from tqdm import tqdm
import pandas as pd

import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.pyplot import cm
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗

for postime in np.arange(1704092660, 1704093260+1):
    aggregated_conflicts = conflicts_part_df[conflicts_part_df['PosTime']==postime]
    Gd = nx.Graph()
    Gd.add_nodes_from(list(np.unique(list(chain.from_iterable(aggregated_conflicts['id_pair'])))))

    # Extract first and second elements from tuples
    ship_pair1 = [pair[0] for pair in aggregated_conflicts['id_pair']]
    ship_pair2 = [pair[1] for pair in aggregated_conflicts['id_pair']]

    edges = [(int(a), int(b), {'weight': float(c)}) for a, b, c in zip(ship_pair1, ship_pair2, aggregated_conflicts['conflict'])]
    Gd.add_edges_from(edges)
    break