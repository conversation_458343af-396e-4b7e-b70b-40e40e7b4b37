#%%
import pandas as pd

conflicts_part_df = pd.read_parquet('result/2024_1/conflicts_part.parquet')
trajectory_note_df = pd.read_parquet('data/2024_1_inter_note.parquet')
#%%
trajectory_note_df.head()
#%%
import networkx as nx
import numpy as np
from itertools import chain
from tqdm import tqdm
import pandas as pd

import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.pyplot import cm

plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗


def determine_ship_relative_position(ship1_data, ship2_data):
    """
    判断两艘船的相对位置关系

    Args:
        ship1_data: 第一艘船数据 (包含lon, lat, cog)
        ship2_data: 第二艘船数据 (包含lon, lat, cog)

    Returns:
        str: 'ship1_ahead' 表示ship1在ship2前方, 'ship1_behind' 表示ship1在ship2后方
    """
    # 计算从ship2到ship1的方向向量
    dx = ship1_data['lon'] - ship2_data['lon']
    dy = ship1_data['lat'] - ship2_data['lat']

    # 将ship2的航向角度转换为弧度 (航向角：0度为正北，顺时针增加)
    ship2_cog_rad = np.radians(90 - ship2_data['cog'])  # 转换为数学角度系统

    # 计算ship2航向方向向量
    cog_dx = np.cos(ship2_cog_rad)
    cog_dy = np.sin(ship2_cog_rad)

    # 计算两个向量的点积
    dot_product = dx * cog_dx + dy * cog_dy

    # 判断相对位置
    if dot_product > 0:
        return 'ship1_ahead'  # ship1在ship2前方
    else:
        return 'ship1_behind'  # ship1在ship2后方


def determine_edge_direction(ship1_id, ship2_id, ship1_data, ship2_data):
    """
    根据船舶Type和相对位置确定有向边的方向

    Args:
        ship1_id: 船舶1的ID
        ship2_id: 船舶2的ID
        ship1_data: 船舶1的数据 (包含type, lon, lat, cog)
        ship2_data: 船舶2的数据 (包含type, lon, lat, cog)

    Returns:
        tuple: (source_id, target_id) 表示边的方向
    """
    ship1_type = ship1_data.get('type', 0)
    ship2_type = ship2_data.get('type', 0)

    # 规则1: 若船舶Type都为0或者都为1，则判断船舶的相对位置和航向，方向为从后方船指向前方船
    if (ship1_type == 0 and ship2_type == 0) or (ship1_type == 1 and ship2_type == 1):
        # 判断ship1相对于ship2的位置
        relative_pos = determine_ship_relative_position(ship1_data, ship2_data)

        if relative_pos == 'ship1_ahead':
            # ship1在ship2前方，边从ship2指向ship1
            return (ship2_id, ship1_id)
        else:
            # ship1在ship2后方，边从ship1指向ship2
            return (ship1_id, ship2_id)

    # 规则2: 若船舶Type一个为0，一个为1，则由1指向0
    elif ship1_type == 1 and ship2_type == 0:
        return (ship1_id, ship2_id)
    elif ship1_type == 0 and ship2_type == 1:
        return (ship2_id, ship1_id)

    # 默认情况（不应该发生）
    else:
        return (ship1_id, ship2_id)


def build_directed_conflict_network(postime):
    """
    构建指定时间点的有向冲突网络

    Args:
        postime: 时间点

    Returns:
        nx.DiGraph: 有向冲突网络
    """
    # 获取该时间点的冲突数据
    aggregated_conflicts = conflicts_part_df.loc[
        (conflicts_part_df['PosTime'] == postime) & (conflicts_part_df['conflict'] > 0)]

    if aggregated_conflicts.empty:
        return nx.DiGraph()

    # 创建有向图
    Gd = nx.DiGraph()

    # 获取该时间点的船舶轨迹数据
    trajectory_at_time = trajectory_note_df[trajectory_note_df['PosTime'] == postime]

    # 创建ID到MMSI的映射和船舶数据字典
    ship_data_dict = {}  # MMSI -> ship_data
    id_to_mmsi = {}      # ID -> MMSI

    for _, row in trajectory_at_time.iterrows():
        mmsi = int(row['MMSI'])
        ship_id = int(row['ID'])

        ship_data_dict[mmsi] = {
            'type': row.get('Type', 0),
            'lon': row['Lon'],
            'lat': row['Lat'],
            'cog': row['Cog']
        }

        id_to_mmsi[ship_id] = mmsi

    # 获取所有涉及冲突的船舶ID，并转换为MMSI
    all_ship_ids = list(np.unique(list(chain.from_iterable(aggregated_conflicts['id_pair']))))
    all_mmsis = []
    for ship_id in all_ship_ids:
        mmsi = id_to_mmsi.get(ship_id)
        if mmsi is not None:
            all_mmsis.append(mmsi)

    Gd.add_nodes_from(all_mmsis)

    # 为每个冲突对确定边的方向
    directed_edges = []
    for _, row in aggregated_conflicts.iterrows():
        ship1_id, ship2_id = row['id_pair']
        conflict_weight = float(row['conflict'])

        # 转换ID到MMSI
        ship1_mmsi = id_to_mmsi.get(ship1_id)
        ship2_mmsi = id_to_mmsi.get(ship2_id)

        if ship1_mmsi is None or ship2_mmsi is None:
            continue

        # 获取船舶数据
        ship1_data = ship_data_dict.get(ship1_mmsi)
        ship2_data = ship_data_dict.get(ship2_mmsi)

        if ship1_data and ship2_data:
            # 确定边的方向
            source_mmsi, target_mmsi = determine_edge_direction(ship1_mmsi, ship2_mmsi, ship1_data, ship2_data)
            directed_edges.append((source_mmsi, target_mmsi, {'weight': conflict_weight}))
        else:
            # 如果缺少船舶数据，默认按MMSI顺序
            directed_edges.append((ship1_mmsi, ship2_mmsi, {'weight': conflict_weight}))

    Gd.add_edges_from(directed_edges)
    return Gd


# 构建有向冲突网络
current_postime = None
for postime in np.arange(1704092660, 1704093260 + 1):
    Gd = build_directed_conflict_network(postime)
    if len(Gd.nodes()) > 0:  # 确保网络不为空
        current_postime = postime
        print(f"构建网络成功: 时间{postime}, 节点{len(Gd.nodes())}, 边{len(Gd.edges())}")
        break

#%%
def vis_directed(nx_graph):
    """可视化有向交通冲突网络"""
    fig, ax = plt.subplots(figsize=(10, 8))
    seed = 4

    if len(nx_graph.nodes()) == 0:
        print("网络为空，无法可视化")
        return

    # 优化布局参数
    try:
        pos = nx.spring_layout(nx_graph, seed=seed, k=2, iterations=50)
    except:
        pos = nx.random_layout(nx_graph, seed=seed)

    # Calculate edge weights and colors
    weights = [nx_graph[u][v]['weight'] for u, v in nx_graph.edges()]
    edge_colors = weights
    camp = cm.YlOrRd

    # 绘制有向图
    nx.draw(nx_graph, pos,
            with_labels=True,
            node_color='lightblue',
            edge_color=edge_colors,
            edge_cmap=camp,
            width=[max(1, w * 5) for w in weights],  # 根据权重调整边宽度
            node_size=300,  # 增大节点以便查看
            font_size=8,
            font_weight='bold',
            font_family='Times New Roman',
            arrows=True,  # 显示箭头表示方向
            arrowsize=20,  # 箭头大小
            arrowstyle='->')

    # 添加颜色条
    if weights:
        sm = plt.cm.ScalarMappable(cmap=camp,
                                   norm=plt.Normalize(vmin=min(weights), vmax=max(weights)))
        sm.set_array([])
        plt.colorbar(sm, ax=ax, shrink=0.8, aspect=25, label='Conflict Weight')

    # 添加标题
    plt.title('Directed Traffic Conflict Network', fontsize=14, fontweight='bold')

    # 添加边框
    rect = Rectangle((0, 0), 1, 1,
                     transform=ax.transAxes,
                     linewidth=1,
                     edgecolor='black',
                     facecolor='none',
                     zorder=10)
    ax.add_patch(rect)

    plt.tight_layout(pad=2)
    plt.show()
    plt.close()
print("\n=== 网络可视化 ===")
vis_directed(Gd)
#%%
# 连通分量真实场景可视化
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse
import os
import pickle

def get_ship_data_at_time(node_id, postime):
    """获取指定时间的船舶数据"""
    # 先尝试按MMSI查找
    ship_data = trajectory_note_df[(trajectory_note_df['MMSI'] == node_id) &
                                  (trajectory_note_df['PosTime'] == postime)]

    # 如果按MMSI找不到，尝试按ID查找
    if ship_data.empty:
        ship_data = trajectory_note_df[(trajectory_note_df['ID'] == node_id) &
                                      (trajectory_note_df['PosTime'] == postime)]

    if ship_data.empty:
        return None

    row = ship_data.iloc[0]
    return {
        'mmsi': int(row['MMSI']),
        'id': int(row['ID']),
        'lon': float(row['Lon']),
        'lat': float(row['Lat']),
        'cog': float(row['Cog']),
        'sog': float(row['Sog']),
        'type': row.get('Type', 0),
        'length': row.get('Length', 100)
    }

def load_base_map():
    """加载底图"""
    map_path = 'data/map0.png'
    if os.path.exists(map_path):
        try:
            return mpimg.imread(map_path)
        except:
            return None
    return None

def draw_channel_boundaries(ax):
    """绘制航道边界线"""
    try:
        with open('data/geo_info.pkl', 'rb') as f:
            geo_info = pickle.load(f)

        if 'channel_side1' in geo_info and geo_info['channel_side1']:
            side1_lons = [p[0] for p in geo_info['channel_side1']]
            side1_lats = [p[1] for p in geo_info['channel_side1']]
            ax.plot(side1_lons, side1_lats, 'green', linewidth=2, alpha=0.6, linestyle='--')

        if 'channel_side2' in geo_info and geo_info['channel_side2']:
            side2_lons = [p[0] for p in geo_info['channel_side2']]
            side2_lats = [p[1] for p in geo_info['channel_side2']]
            ax.plot(side2_lons, side2_lats, 'green', linewidth=2, alpha=0.6, linestyle='--')

        if 'channel_centerline' in geo_info and geo_info['channel_centerline']:
            center_lons = [p[0] for p in geo_info['channel_centerline']]
            center_lats = [p[1] for p in geo_info['channel_centerline']]
            ax.plot(center_lons, center_lats, 'green', linewidth=1.5, alpha=0.5, linestyle='-')
    except:
        pass

def draw_ship_domain(ax, lon, lat, cog, color, ship_length, alpha=0.3):
    """绘制船舶领域椭圆"""
    if ship_length <= 100:
        a_meters, b_meters = 271, 192
    else:
        a_meters, b_meters = 375, 210

    lat_rad = np.radians(lat)
    meters_per_degree_lon = 111320 * np.cos(lat_rad)
    meters_per_degree_lat = 111320

    a_degrees = a_meters / meters_per_degree_lon
    b_degrees = b_meters / meters_per_degree_lat
    angle = 90 - cog

    ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, angle=angle,
                      facecolor=color, alpha=alpha, edgecolor=color, linewidth=1.5)
    ax.add_patch(ellipse)

def visualize_component_real_scene(G, component_nodes, postime, component_idx):
    """可视化连通分量的真实场景"""
    # 获取船舶数据
    ship_positions = []
    for mmsi in component_nodes:
        ship_data = get_ship_data_at_time(mmsi, postime)
        if ship_data:
            ship_positions.append(ship_data)

    if not ship_positions:
        print(f"连通分量 {component_idx} 无有效船舶数据")
        return

    # 计算显示范围 - 大幅扩大显示区域
    lons = [pos['lon'] for pos in ship_positions]
    lats = [pos['lat'] for pos in ship_positions]

    lon_center = (max(lons) + min(lons)) / 2
    lat_center = (max(lats) + min(lats)) / 2

    # 设置固定的显示范围，确保能看到足够大的区域
    display_range = 0.15  # 显示范围±0.15度，约16公里

    display_extent = [
        lon_center - display_range, lon_center + display_range,
        lat_center - display_range, lat_center + display_range
    ]

    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 10))

    # 加载底图
    base_map = load_base_map()
    if base_map is not None:
        base_map_extent = [121.050, 121.350, 31.516, 31.784]
        ax.imshow(base_map, extent=base_map_extent, aspect='auto', alpha=0.7)

    ax.set_xlim(display_extent[0], display_extent[1])
    ax.set_ylim(display_extent[2], display_extent[3])

    # 绘制航道边界
    draw_channel_boundaries(ax)

    # 定义颜色
    colors = ['red', 'blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

    # 绘制每艘船舶
    for i, ship_data in enumerate(ship_positions):
        color = colors[i % len(colors)]

        # 绘制船舶领域椭圆
        draw_ship_domain(ax, ship_data['lon'], ship_data['lat'], ship_data['cog'],
                        color, ship_data['length'], alpha=0.25)

        # 绘制船舶位置
        ax.plot(ship_data['lon'], ship_data['lat'], 'o', color=color, markersize=16,
               markeredgecolor='black', markeredgewidth=2, zorder=5)

        # 在船舶位置上显示ID
        display_id = ship_data.get('id', ship_data['mmsi'])
        ax.text(ship_data['lon'], ship_data['lat'], str(display_id),
               fontsize=8, ha='center', va='center', fontweight='bold',
               color='white', zorder=6)

        # 标注船舶信息
        info_text = f"ID: {display_id}\nMMSI: {ship_data['mmsi']}\nType: {ship_data['type']}\nCOG: {ship_data['cog']:.1f}°\nSOG: {ship_data['sog']:.1f}kn"
        ax.annotate(info_text,
                   (ship_data['lon'], ship_data['lat']),
                   xytext=(25, 25), textcoords='offset points',
                   fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.4', facecolor='white', alpha=0.9, edgecolor='black'))

        # 绘制航向箭头
        arrow_length = min(lon_range, lat_range) * 0.04
        dx = arrow_length * np.sin(np.radians(ship_data['cog']))
        dy = arrow_length * np.cos(np.radians(ship_data['cog']))

        ax.annotate('',
                   xy=(ship_data['lon'] + dx, ship_data['lat'] + dy),
                   xytext=(ship_data['lon'], ship_data['lat']),
                   arrowprops=dict(arrowstyle='->', color=color, lw=2, alpha=0.9, mutation_scale=15),
                   zorder=4)

    # 绘制冲突关系（有向边）
    subgraph = G.subgraph(component_nodes)
    for source, target, data in subgraph.edges(data=True):
        source_data = next((s for s in ship_positions if s['mmsi'] == source), None)
        target_data = next((s for s in ship_positions if s['mmsi'] == target), None)

        if source_data and target_data:
            # 计算箭头位置，避免与船舶重叠
            dx = target_data['lon'] - source_data['lon']
            dy = target_data['lat'] - source_data['lat']

            shrink_factor = 0.15
            start_lon = source_data['lon'] + dx * shrink_factor
            start_lat = source_data['lat'] + dy * shrink_factor
            end_lon = target_data['lon'] - dx * shrink_factor
            end_lat = target_data['lat'] - dy * shrink_factor

            # 绘制粗箭头表示冲突方向
            ax.annotate('', xy=(end_lon, end_lat), xytext=(start_lon, start_lat),
                       arrowprops=dict(arrowstyle='->', color='red', lw=5, alpha=0.9,
                                     mutation_scale=25, shrinkA=0, shrinkB=0))

            # 标注方向和权重
            mid_lon = (start_lon + end_lon) / 2
            mid_lat = (start_lat + end_lat) / 2
            weight = data['weight']

            # 方向标识 - 显示实际的ID而不是MMSI
            source_display_id = source_data.get('id', source)
            target_display_id = target_data.get('id', target)
            direction_text = f'{source_display_id} → {target_display_id}'
            ax.text(mid_lon, mid_lat + lat_range * 0.015, direction_text,
                   fontsize=9, ha='center', va='center', fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.9, edgecolor='blue'))

            # 冲突权重
            ax.text(mid_lon, mid_lat - lat_range * 0.015, f'冲突: {weight:.3f}',
                   fontsize=10, ha='center', va='center', fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.9, edgecolor='red'))

            # 方向指示符
            ax.plot(mid_lon, mid_lat, 'r^', markersize=12, markeredgecolor='darkred',
                   markeredgewidth=2, zorder=7, alpha=0.9)

    # 设置标题和标签
    ax.set_title(f'有向交通冲突网络 - 连通分量 {component_idx}\n'
                f'时间: {postime} | 船舶数: {len(ship_positions)} | 冲突边数: {subgraph.number_of_edges()}',
                fontsize=14, fontweight='bold', pad=20)
    ax.set_xlabel('经度 (Longitude)', fontsize=12)
    ax.set_ylabel('纬度 (Latitude)', fontsize=12)
    ax.grid(True, alpha=0.3)

    # # 添加图例
    # legend_elements = []
    # legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
    #                                 markersize=10, label='船舶位置', markeredgecolor='black'))
    # legend_elements.append(plt.Line2D([0], [0], color='red', linewidth=4,
    #                                 label='冲突方向 (A→B: A对B产生冲突)'))
    # legend_elements.append(plt.Line2D([0], [0], marker='^', color='red', markersize=8,
    #                                 label='方向指示符', linestyle='None'))
    # legend_elements.append(Ellipse((0, 0), 0, 0, facecolor='blue', alpha=0.3, label='船舶安全领域'))
    # legend_elements.append(plt.Line2D([0], [0], color='green', linewidth=2,
    #                                 linestyle='--', alpha=0.6, label='航道边界'))
    #
    # ax.legend(handles=legend_elements, loc='upper right', fontsize=9,
    #          bbox_to_anchor=(1.0, 1.0), framealpha=0.9)

    plt.tight_layout()
    plt.show()
    plt.close()

# 分析并可视化所有连通分量
if len(Gd.nodes()) > 0 and current_postime is not None:
    print(f"\n=== 连通分量真实场景分析 (时间: {current_postime}) ===")

    # 获取弱连通分量
    weakly_connected_components = list(nx.weakly_connected_components(Gd))
    print(f"弱连通分量数量: {len(weakly_connected_components)}")

    # 可视化每个连通分量
    for i, component in enumerate(weakly_connected_components):
        component_nodes = list(component)
        subgraph = Gd.subgraph(component_nodes)

        print(f"\n连通分量 {i+1}:")
        print(f"  节点数: {len(component_nodes)}")
        print(f"  边数: {subgraph.number_of_edges()}")
        print(f"  节点: {component_nodes}")

        # 显示节点对应的实际ID
        node_info = []
        for node_id in component_nodes:
            ship_data = get_ship_data_at_time(node_id, current_postime)
            if ship_data:
                actual_id = ship_data.get('id', node_id)
                node_info.append(f"节点{node_id}->ID{actual_id}")
            else:
                node_info.append(f"节点{node_id}->无数据")
        print(f"  节点映射: {', '.join(node_info)}")

        # 可视化该连通分量
        visualize_component_real_scene(Gd, component_nodes, current_postime, i+1)
else:
    print("❌ 网络为空或没有找到有效的时间点数据")