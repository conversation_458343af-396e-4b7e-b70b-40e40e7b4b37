#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试有向交通冲突网络的连通分量可视化
"""

import pandas as pd
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse
import os
from itertools import chain

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据"""
    try:
        conflicts_df = pd.read_parquet('result/2024_1/conflicts_part.parquet')
        trajectory_df = pd.read_parquet('data/2024_1_inter_note.parquet')
        
        print("✅ 数据加载成功")
        print(f"冲突数据: {conflicts_df.shape}")
        print(f"轨迹数据: {trajectory_df.shape}")
        
        return conflicts_df, trajectory_df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def determine_ship_relative_position(ship1_data, ship2_data):
    """判断两艘船的相对位置关系"""
    dx = ship1_data['lon'] - ship2_data['lon']
    dy = ship1_data['lat'] - ship2_data['lat']
    
    ship2_cog_rad = np.radians(90 - ship2_data['cog'])
    cog_dx = np.cos(ship2_cog_rad)
    cog_dy = np.sin(ship2_cog_rad)
    
    dot_product = dx * cog_dx + dy * cog_dy
    
    return 'ship1_ahead' if dot_product > 0 else 'ship1_behind'

def determine_edge_direction(ship1_id, ship2_id, ship1_data, ship2_data):
    """确定有向边的方向"""
    ship1_type = ship1_data.get('type', 0)
    ship2_type = ship2_data.get('type', 0)
    
    if (ship1_type == 0 and ship2_type == 0) or (ship1_type == 1 and ship2_type == 1):
        relative_pos = determine_ship_relative_position(ship1_data, ship2_data)
        if relative_pos == 'ship1_ahead':
            return (ship2_id, ship1_id)
        else:
            return (ship1_id, ship2_id)
    elif ship1_type == 1 and ship2_type == 0:
        return (ship1_id, ship2_id)
    elif ship1_type == 0 and ship2_type == 1:
        return (ship2_id, ship1_id)
    else:
        return (ship1_id, ship2_id)

def build_directed_conflict_network(conflicts_df, trajectory_df, postime):
    """构建有向冲突网络"""
    aggregated_conflicts = conflicts_df.loc[
        (conflicts_df['PosTime'] == postime) & (conflicts_df['conflict'] > 0)]
    
    if aggregated_conflicts.empty:
        return nx.DiGraph(), postime
    
    Gd = nx.DiGraph()
    trajectory_at_time = trajectory_df[trajectory_df['PosTime'] == postime]
    
    ship_data_dict = {}
    for _, row in trajectory_at_time.iterrows():
        ship_data_dict[int(row['MMSI'])] = {
            'type': row.get('Type', 0),
            'lon': row['Lon'],
            'lat': row['Lat'],
            'cog': row['Cog'],
            'sog': row.get('Sog', 0),
            'length': row.get('Length', 100)
        }
    
    all_ships = list(np.unique(list(chain.from_iterable(aggregated_conflicts['id_pair']))))
    Gd.add_nodes_from(all_ships)
    
    directed_edges = []
    for _, row in aggregated_conflicts.iterrows():
        ship1_id, ship2_id = row['id_pair']
        conflict_weight = float(row['conflict'])
        
        ship1_data = ship_data_dict.get(ship1_id)
        ship2_data = ship_data_dict.get(ship2_id)
        
        if ship1_data and ship2_data:
            source_id, target_id = determine_edge_direction(ship1_id, ship2_id, ship1_data, ship2_data)
            directed_edges.append((source_id, target_id, {'weight': conflict_weight}))
        else:
            directed_edges.append((ship1_id, ship2_id, {'weight': conflict_weight}))
    
    Gd.add_edges_from(directed_edges)
    return Gd, postime

def visualize_connected_component_simple(G, component_nodes, postime, trajectory_df, component_idx):
    """简化版连通分量可视化"""
    # 获取船舶数据
    ship_positions = []
    for mmsi in component_nodes:
        ship_data = trajectory_df[(trajectory_df['MMSI'] == mmsi) & 
                                 (trajectory_df['PosTime'] == postime)]
        if not ship_data.empty:
            row = ship_data.iloc[0]
            ship_positions.append({
                'mmsi': int(row['MMSI']),
                'lon': float(row['Lon']),
                'lat': float(row['Lat']),
                'cog': float(row['Cog']),
                'type': row.get('Type', 0),
                'length': row.get('Length', 100)
            })
    
    if not ship_positions:
        print(f"连通分量 {component_idx} 无有效船舶数据")
        return
    
    # 计算显示范围
    lons = [pos['lon'] for pos in ship_positions]
    lats = [pos['lat'] for pos in ship_positions]
    
    lon_min, lon_max = min(lons), max(lons)
    lat_min, lat_max = min(lats), max(lats)
    
    lon_range = max(lon_max - lon_min, 0.01)
    lat_range = max(lat_max - lat_min, 0.01)
    
    expand = 0.3
    display_extent = [
        lon_min - lon_range * expand, lon_max + lon_range * expand,
        lat_min - lat_range * expand, lat_max + lat_range * expand
    ]
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # 设置显示范围
    ax.set_xlim(display_extent[0], display_extent[1])
    ax.set_ylim(display_extent[2], display_extent[3])
    
    # 定义颜色
    colors = ['red', 'blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    # 绘制每艘船舶
    for i, ship_data in enumerate(ship_positions):
        color = colors[i % len(colors)]
        
        # 绘制船舶位置
        ax.plot(ship_data['lon'], ship_data['lat'], 'o', color=color, markersize=12, 
               markeredgecolor='black', markeredgewidth=2)
        
        # 标注船舶编号
        ax.annotate(f"{ship_data['mmsi']}\n(T:{ship_data['type']})", 
                   (ship_data['lon'], ship_data['lat']),
                   xytext=(15, 15), textcoords='offset points',
                   fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 绘制航向箭头
        arrow_length = min(lon_range, lat_range) * 0.1
        dx = arrow_length * np.sin(np.radians(ship_data['cog']))
        dy = arrow_length * np.cos(np.radians(ship_data['cog']))
        
        ax.arrow(ship_data['lon'], ship_data['lat'], dx, dy,
                head_width=arrow_length*0.3, head_length=arrow_length*0.2, 
                fc=color, ec=color, linewidth=3, alpha=0.8)
    
    # 绘制冲突关系
    subgraph = G.subgraph(component_nodes)
    for source, target, data in subgraph.edges(data=True):
        source_data = next((s for s in ship_positions if s['mmsi'] == source), None)
        target_data = next((s for s in ship_positions if s['mmsi'] == target), None)
        
        if source_data and target_data:
            # 绘制冲突连线
            ax.annotate('', xy=(target_data['lon'], target_data['lat']),
                       xytext=(source_data['lon'], source_data['lat']),
                       arrowprops=dict(arrowstyle='->', color='red', lw=3, alpha=0.8))
            
            # 标注冲突权重
            mid_lon = (source_data['lon'] + target_data['lon']) / 2
            mid_lat = (source_data['lat'] + target_data['lat']) / 2
            weight = data['weight']
            ax.text(mid_lon, mid_lat, f'{weight:.3f}', 
                   fontsize=9, ha='center', va='center', fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='yellow', alpha=0.8))
    
    # 设置标题
    ax.set_title(f'连通分量 {component_idx} - 真实会遇场景\n'
                f'时间: {postime}, 船舶数: {len(ship_positions)}, 冲突边数: {subgraph.number_of_edges()}',
                fontsize=14, fontweight='bold')
    ax.set_xlabel('经度')
    ax.set_ylabel('纬度')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    os.makedirs('vis/directed_network_scenes', exist_ok=True)
    output_file = f'vis/directed_network_scenes/component_{component_idx}_time_{postime}.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 连通分量 {component_idx} 可视化完成: {output_file}")
    return output_file

def main():
    """主函数"""
    print("=== 有向交通冲突网络连通分量可视化测试 ===")
    
    # 加载数据
    conflicts_df, trajectory_df = load_data()
    if conflicts_df is None or trajectory_df is None:
        return
    
    # 寻找有冲突的时间点
    conflict_times = conflicts_df[conflicts_df['conflict'] > 0]['PosTime'].unique()
    if len(conflict_times) == 0:
        print("❌ 没有找到有冲突的时间点")
        return
    
    # 选择第一个有冲突的时间点
    postime = sorted(conflict_times)[0]
    print(f"\n选择时间点: {postime}")
    
    # 构建有向网络
    G, _ = build_directed_conflict_network(conflicts_df, trajectory_df, postime)
    
    if len(G.nodes()) == 0:
        print("❌ 构建的网络为空")
        return
    
    print(f"✅ 网络构建成功: {G.number_of_nodes()} 节点, {G.number_of_edges()} 边")
    
    # 分析连通分量
    weakly_connected_components = list(nx.weakly_connected_components(G))
    print(f"\n弱连通分量数量: {len(weakly_connected_components)}")
    
    # 可视化每个连通分量
    output_files = []
    for i, component in enumerate(weakly_connected_components):
        component_nodes = list(component)
        print(f"\n连通分量 {i+1}: {len(component_nodes)} 个节点 {component_nodes}")
        
        try:
            output_file = visualize_connected_component_simple(
                G, component_nodes, postime, trajectory_df, i+1)
            if output_file:
                output_files.append(output_file)
        except Exception as e:
            print(f"❌ 连通分量 {i+1} 可视化失败: {e}")
    
    print(f"\n✅ 完成! 生成了 {len(output_files)} 个可视化文件")
    for file in output_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
