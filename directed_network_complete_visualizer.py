#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有向交通冲突网络完整可视化器
参考43.conflict_example_vis.py，绘制Gd中每一个最大连通子图的真实会遇情况，标注节点编号
"""

import pandas as pd
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import matplotlib.patches as patches
from matplotlib.patches import Ellipse
import os
from itertools import chain
import pickle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DirectedNetworkCompleteVisualizer:
    """有向网络完整可视化器"""
    
    def __init__(self):
        self.base_map = None
        self.base_map_extent = [121.050, 121.350, 31.516, 31.784]
        self._load_base_map()
        
    def _load_base_map(self):
        """加载底图"""
        map_path = 'data/map0.png'
        if os.path.exists(map_path):
            try:
                self.base_map = mpimg.imread(map_path)
                print("✅ 底图加载成功")
            except Exception as e:
                self.base_map = None
                print(f"⚠️ 底图加载失败: {e}")
        else:
            print("⚠️ 底图文件不存在")
                
    def _draw_channel_boundaries(self, ax):
        """绘制航道边界线"""
        try:
            with open('data/geo_info.pkl', 'rb') as f:
                geo_info = pickle.load(f)

            # 绘制航道边界1
            if 'channel_side1' in geo_info:
                side1_data = geo_info['channel_side1']
                if side1_data and len(side1_data) > 0:
                    side1_lons = [p[0] for p in side1_data]
                    side1_lats = [p[1] for p in side1_data]
                    ax.plot(side1_lons, side1_lats, 'green', linewidth=2, alpha=0.6, linestyle='--', label='航道边界')

            # 绘制航道边界2
            if 'channel_side2' in geo_info:
                side2_data = geo_info['channel_side2']
                if side2_data and len(side2_data) > 0:
                    side2_lons = [p[0] for p in side2_data]
                    side2_lats = [p[1] for p in side2_data]
                    ax.plot(side2_lons, side2_lats, 'green', linewidth=2, alpha=0.6, linestyle='--')

            # 绘制航道中心线
            if 'channel_centerline' in geo_info:
                centerline_data = geo_info['channel_centerline']
                if centerline_data and len(centerline_data) > 0:
                    center_lons = [p[0] for p in centerline_data]
                    center_lats = [p[1] for p in centerline_data]
                    ax.plot(center_lons, center_lats, 'green', linewidth=1.5, alpha=0.5, linestyle='-', label='航道中心线')

        except Exception as e:
            print(f"⚠️ 绘制航道边界失败: {e}")
    
    def _draw_ship_domain(self, ax, lon, lat, cog, color, ship_length, alpha=0.3):
        """绘制船舶领域椭圆"""
        # 根据船舶长度获取椭圆参数
        if ship_length <= 100:
            a_meters, b_meters = 271, 192  # 小船
        else:
            a_meters, b_meters = 375, 210  # 大船

        # 将米转换为度
        lat_rad = np.radians(lat)
        meters_per_degree_lon = 111320 * np.cos(lat_rad)
        meters_per_degree_lat = 111320

        a_degrees = a_meters / meters_per_degree_lon
        b_degrees = b_meters / meters_per_degree_lat

        # 航向转换
        angle = 90 - cog

        # 创建椭圆
        ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, angle=angle,
                          facecolor=color, alpha=alpha, edgecolor=color, linewidth=1.5)
        ax.add_patch(ellipse)

        return a_meters, b_meters
    
    def determine_ship_relative_position(self, ship1_data, ship2_data):
        """判断两艘船的相对位置关系"""
        dx = ship1_data['lon'] - ship2_data['lon']
        dy = ship1_data['lat'] - ship2_data['lat']
        
        ship2_cog_rad = np.radians(90 - ship2_data['cog'])
        cog_dx = np.cos(ship2_cog_rad)
        cog_dy = np.sin(ship2_cog_rad)
        
        dot_product = dx * cog_dx + dy * cog_dy
        
        return 'ship1_ahead' if dot_product > 0 else 'ship1_behind'

    def determine_edge_direction(self, ship1_mmsi, ship2_mmsi, ship1_data, ship2_data):
        """确定有向边的方向"""
        ship1_type = ship1_data.get('type', 0)
        ship2_type = ship2_data.get('type', 0)
        
        if (ship1_type == 0 and ship2_type == 0) or (ship1_type == 1 and ship2_type == 1):
            relative_pos = self.determine_ship_relative_position(ship1_data, ship2_data)
            if relative_pos == 'ship1_ahead':
                return (ship2_mmsi, ship1_mmsi)  # 从后方指向前方
            else:
                return (ship1_mmsi, ship2_mmsi)
        elif ship1_type == 1 and ship2_type == 0:
            return (ship1_mmsi, ship2_mmsi)  # 1指向0
        elif ship1_type == 0 and ship2_type == 1:
            return (ship2_mmsi, ship1_mmsi)  # 1指向0
        else:
            return (ship1_mmsi, ship2_mmsi)

    def build_directed_conflict_network(self, conflicts_df, trajectory_df, postime):
        """构建有向冲突网络"""
        aggregated_conflicts = conflicts_df.loc[
            (conflicts_df['PosTime'] == postime) & (conflicts_df['conflict'] > 0)]
        
        if aggregated_conflicts.empty:
            return nx.DiGraph(), {}
        
        Gd = nx.DiGraph()
        trajectory_at_time = trajectory_df[trajectory_df['PosTime'] == postime]
        
        # 创建ID到MMSI的映射和船舶数据字典
        ship_data_dict = {}  # MMSI -> ship_data
        id_to_mmsi = {}      # ID -> MMSI
        
        for _, row in trajectory_at_time.iterrows():
            mmsi = int(row['MMSI'])
            ship_id = int(row['ID'])
            
            ship_data_dict[mmsi] = {
                'type': row.get('Type', 0),
                'lon': row['Lon'],
                'lat': row['Lat'],
                'cog': row['Cog'],
                'sog': row.get('Sog', 0),
                'length': row.get('Length', 100)
            }
            
            id_to_mmsi[ship_id] = mmsi
        
        # 使用MMSI作为节点
        all_ship_ids = list(np.unique(list(chain.from_iterable(aggregated_conflicts['id_pair']))))
        all_mmsis = [id_to_mmsi.get(ship_id) for ship_id in all_ship_ids]
        all_mmsis = [mmsi for mmsi in all_mmsis if mmsi is not None]
        
        Gd.add_nodes_from(all_mmsis)
        
        directed_edges = []
        for _, row in aggregated_conflicts.iterrows():
            ship1_id, ship2_id = row['id_pair']
            conflict_weight = float(row['conflict'])
            
            # 转换ID到MMSI
            ship1_mmsi = id_to_mmsi.get(ship1_id)
            ship2_mmsi = id_to_mmsi.get(ship2_id)
            
            if ship1_mmsi is None or ship2_mmsi is None:
                continue
                
            ship1_data = ship_data_dict.get(ship1_mmsi)
            ship2_data = ship_data_dict.get(ship2_mmsi)
            
            if ship1_data and ship2_data:
                source_mmsi, target_mmsi = self.determine_edge_direction(ship1_mmsi, ship2_mmsi, ship1_data, ship2_data)
                directed_edges.append((source_mmsi, target_mmsi, {'weight': conflict_weight}))
        
        Gd.add_edges_from(directed_edges)
        return Gd, ship_data_dict
    
    def visualize_connected_component(self, G, component_nodes, postime, ship_data_dict, component_idx, 
                                    output_dir='vis/directed_network_scenes'):
        """可视化单个连通分量的真实场景"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取船舶数据
        ship_positions = []
        for mmsi in component_nodes:
            if mmsi in ship_data_dict:
                ship_data = ship_data_dict[mmsi]
                ship_positions.append({
                    'mmsi': mmsi,
                    'lon': ship_data['lon'],
                    'lat': ship_data['lat'],
                    'cog': ship_data['cog'],
                    'type': ship_data['type'],
                    'length': ship_data['length'],
                    'sog': ship_data['sog']
                })
        
        if not ship_positions:
            print(f"连通分量 {component_idx} 无有效船舶数据")
            return None
        
        # 计算显示范围
        lons = [pos['lon'] for pos in ship_positions]
        lats = [pos['lat'] for pos in ship_positions]
        
        lon_min, lon_max = min(lons), max(lons)
        lat_min, lat_max = min(lats), max(lats)
        
        lon_range = max(lon_max - lon_min, 0.01)
        lat_range = max(lat_max - lat_min, 0.01)
        
        expand = 0.4
        display_extent = [
            lon_min - lon_range * expand, lon_max + lon_range * expand,
            lat_min - lat_range * expand, lat_max + lat_range * expand
        ]
        
        # 计算图片尺寸
        center_lat = (lat_min + lat_max) / 2
        lat_km_per_degree = 111.0
        lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))
        
        actual_lon_distance = (display_extent[1] - display_extent[0]) * lon_km_per_degree
        actual_lat_distance = (display_extent[3] - display_extent[2]) * lat_km_per_degree
        aspect_ratio = actual_lat_distance / actual_lon_distance
        
        base_width = 14
        height = max(8, min(base_width * aspect_ratio, 18))
        figsize = (base_width, height)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 设置底图
        if self.base_map is not None:
            ax.imshow(self.base_map, extent=self.base_map_extent, aspect='auto', alpha=0.7)
        
        # 设置显示范围
        ax.set_xlim(display_extent[0], display_extent[1])
        ax.set_ylim(display_extent[2], display_extent[3])
        
        # 绘制航道边界线
        self._draw_channel_boundaries(ax)
        
        # 定义颜色
        colors = ['red', 'blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan', 'magenta']
        
        # 绘制每艘船舶
        for i, ship_data in enumerate(ship_positions):
            color = colors[i % len(colors)]
            
            # 绘制船舶位置
            ax.plot(ship_data['lon'], ship_data['lat'], 'o', color=color, markersize=14, 
                   markeredgecolor='black', markeredgewidth=2, zorder=5)
            
            # 绘制船舶领域
            self._draw_ship_domain(ax, ship_data['lon'], ship_data['lat'], ship_data['cog'], 
                                 color, ship_data['length'], alpha=0.25)
            
            # 标注船舶编号和信息
            info_text = f"MMSI: {ship_data['mmsi']}\nType: {ship_data['type']}\nCOG: {ship_data['cog']:.1f}°\nSOG: {ship_data['sog']:.1f}kn"
            ax.annotate(info_text, 
                       (ship_data['lon'], ship_data['lat']),
                       xytext=(20, 20), textcoords='offset points',
                       fontsize=9, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.4', facecolor='white', alpha=0.9, edgecolor='black'))
            
            # 绘制航向箭头
            arrow_length = min(lon_range, lat_range) * 0.08
            dx = arrow_length * np.sin(np.radians(ship_data['cog']))
            dy = arrow_length * np.cos(np.radians(ship_data['cog']))
            
            ax.arrow(ship_data['lon'], ship_data['lat'], dx, dy,
                    head_width=arrow_length*0.25, head_length=arrow_length*0.15, 
                    fc=color, ec=color, linewidth=3, alpha=0.9, zorder=4)
        
        # 绘制冲突关系（有向边）
        subgraph = G.subgraph(component_nodes)
        for source, target, data in subgraph.edges(data=True):
            source_data = next((s for s in ship_positions if s['mmsi'] == source), None)
            target_data = next((s for s in ship_positions if s['mmsi'] == target), None)

            if source_data and target_data:
                # 计算箭头位置，避免与船舶重叠
                dx = target_data['lon'] - source_data['lon']
                dy = target_data['lat'] - source_data['lat']

                # 缩短箭头，避免与船舶圆点重叠
                shrink_factor = 0.15  # 缩短15%
                start_lon = source_data['lon'] + dx * shrink_factor
                start_lat = source_data['lat'] + dy * shrink_factor
                end_lon = target_data['lon'] - dx * shrink_factor
                end_lat = target_data['lat'] - dy * shrink_factor

                # 绘制粗箭头表示冲突方向
                ax.annotate('', xy=(end_lon, end_lat),
                           xytext=(start_lon, start_lat),
                           arrowprops=dict(
                               arrowstyle='->',
                               color='red',
                               lw=5,  # 更粗的线条
                               alpha=0.9,
                               shrinkA=0, shrinkB=0,
                               mutation_scale=25  # 更大的箭头
                           ),
                           zorder=3)

                # 在箭头旁边添加方向标识
                mid_lon = (start_lon + end_lon) / 2
                mid_lat = (start_lat + end_lat) / 2
                weight = data['weight']

                # 计算箭头方向角度
                angle = np.degrees(np.arctan2(dy, dx))

                # 添加方向标识文本
                direction_text = f'{source} → {target}'
                ax.text(mid_lon, mid_lat + 0.001, direction_text,
                       fontsize=9, ha='center', va='center', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.9, edgecolor='blue'),
                       zorder=6)

                # 标注冲突权重
                ax.text(mid_lon, mid_lat - 0.001, f'冲突: {weight:.3f}',
                       fontsize=10, ha='center', va='center', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.9, edgecolor='red'),
                       zorder=6)

                # 在箭头中点添加方向指示符
                ax.plot(mid_lon, mid_lat, 'r^', markersize=12, markeredgecolor='darkred',
                       markeredgewidth=2, zorder=7, alpha=0.9)
        
        # 设置标题和标签
        ax.set_title(f'有向交通冲突网络 - 连通分量 {component_idx}\n'
                    f'时间: {postime} | 船舶数: {len(ship_positions)} | 冲突边数: {subgraph.number_of_edges()}',
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('经度 (Longitude)', fontsize=12)
        ax.set_ylabel('纬度 (Latitude)', fontsize=12)
        
        # 添加网格和图例
        ax.grid(True, alpha=0.3)
        
        # 添加详细图例
        legend_elements = []
        legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
                                        markersize=10, label='船舶位置', markeredgecolor='black'))
        legend_elements.append(plt.Line2D([0], [0], color='red', linewidth=4,
                                        label='冲突方向 (A→B: A对B产生冲突)'))
        legend_elements.append(plt.Line2D([0], [0], marker='^', color='red', markersize=8,
                                        label='方向指示符', linestyle='None'))
        legend_elements.append(patches.Ellipse((0, 0), 0, 0, facecolor='blue', alpha=0.3,
                                               label='船舶安全领域'))
        if hasattr(ax, '_legend_added'):  # 避免重复添加航道图例
            pass
        else:
            legend_elements.append(plt.Line2D([0], [0], color='green', linewidth=2,
                                            linestyle='--', alpha=0.6, label='航道边界'))
            ax._legend_added = True

        ax.legend(handles=legend_elements, loc='upper right', fontsize=9,
                 bbox_to_anchor=(1.0, 1.0), framealpha=0.9)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(output_dir, f'component_{component_idx}_time_{postime}.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 连通分量 {component_idx} 可视化完成: {output_file}")
        return output_file

    def analyze_and_visualize_time_point(self, conflicts_df, trajectory_df, postime):
        """分析并可视化单个时间点的网络"""
        print(f"\n=== 分析时间点: {postime} ===")

        # 构建有向网络
        G, ship_data_dict = self.build_directed_conflict_network(conflicts_df, trajectory_df, postime)

        if len(G.nodes()) == 0:
            print("❌ 该时间点无冲突网络")
            return []

        print(f"✅ 网络构建成功: {G.number_of_nodes()} 节点, {G.number_of_edges()} 边")

        # 分析连通分量
        weakly_connected_components = list(nx.weakly_connected_components(G))
        strongly_connected_components = list(nx.strongly_connected_components(G))

        print(f"弱连通分量数量: {len(weakly_connected_components)}")
        print(f"强连通分量数量: {len(strongly_connected_components)}")

        # 可视化每个弱连通分量
        output_files = []
        for i, component in enumerate(weakly_connected_components):
            component_nodes = list(component)
            subgraph = G.subgraph(component_nodes)

            print(f"\n连通分量 {i+1}:")
            print(f"  节点数: {len(component_nodes)}")
            print(f"  边数: {subgraph.number_of_edges()}")
            print(f"  节点MMSI: {component_nodes}")

            try:
                output_file = self.visualize_connected_component(
                    G, component_nodes, postime, ship_data_dict, i+1)
                if output_file:
                    output_files.append(output_file)
            except Exception as e:
                print(f"  ❌ 可视化失败: {e}")
                import traceback
                traceback.print_exc()

        return output_files

    def batch_analyze_multiple_times(self, conflicts_df, trajectory_df, max_time_points=5):
        """批量分析多个时间点"""
        print("=== 批量分析多个时间点 ===")

        # 获取有冲突的时间点
        conflict_times = conflicts_df[conflicts_df['conflict'] > 0]['PosTime'].unique()
        conflict_times = sorted(conflict_times)

        print(f"总共有 {len(conflict_times)} 个时间点存在冲突")

        # 限制分析的时间点数量
        selected_times = conflict_times[:max_time_points]
        print(f"选择分析前 {len(selected_times)} 个时间点")

        all_output_files = []

        for postime in selected_times:
            try:
                output_files = self.analyze_and_visualize_time_point(conflicts_df, trajectory_df, postime)
                all_output_files.extend(output_files)
            except Exception as e:
                print(f"❌ 时间点 {postime} 分析失败: {e}")

        print(f"\n✅ 批量分析完成! 总共生成 {len(all_output_files)} 个可视化文件")
        return all_output_files

def main():
    """主函数"""
    print("=== 有向交通冲突网络完整可视化器 ===")

    # 加载数据
    try:
        conflicts_df = pd.read_parquet('result/2024_1/conflicts_part.parquet')
        trajectory_df = pd.read_parquet('data/2024_1_inter_note.parquet')
        print("✅ 数据加载成功")
        print(f"冲突数据: {conflicts_df.shape}")
        print(f"轨迹数据: {trajectory_df.shape}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return

    # 创建可视化器
    visualizer = DirectedNetworkCompleteVisualizer()

    # 选择分析模式
    mode = 'batch'  # 'single' 或 'batch'

    if mode == 'single':
        # 单个时间点分析
        conflict_times = conflicts_df[conflicts_df['conflict'] > 0]['PosTime'].unique()
        if len(conflict_times) == 0:
            print("❌ 没有找到有冲突的时间点")
            return

        postime = sorted(conflict_times)[0]  # 选择第一个有冲突的时间点
        output_files = visualizer.analyze_and_visualize_time_point(conflicts_df, trajectory_df, postime)

        print(f"\n✅ 单时间点分析完成! 生成文件:")
        for file in output_files:
            print(f"  - {file}")

    elif mode == 'batch':
        # 批量分析多个时间点
        output_files = visualizer.batch_analyze_multiple_times(conflicts_df, trajectory_df, max_time_points=3)

        print(f"\n✅ 批量分析完成! 生成文件:")
        for file in output_files:
            print(f"  - {file}")

if __name__ == "__main__":
    main()
